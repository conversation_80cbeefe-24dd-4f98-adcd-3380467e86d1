from simulation import CUBE
import neat
import os
import pickle
import random
import time
import threading
# 2D visualization imports
try:
    import pyglet
    from pyglet import shapes
    VISUALIZATION_AVAILABLE = True
except ImportError:
    print("Warning: Pyglet not available for visualization.")
    print("Install with: pip install pyglet")
    VISUALIZATION_AVAILABLE = False

best_so_far = float('-inf')
current_best_genome = None
visualization_running = False

class CubeVisualizer:
    def __init__(self):
        if not VISUALIZATION_AVAILABLE:
            return

        self.window = None
        self.cube_state = [0]*54
        self.move_delay = 0.5  # Delay between moves for better visualization
        self.batch = None
        self.squares = []
        self.current_move = ""
        self.fitness_score = 0

        # Color mapping for cube faces (RGB values)
        self.colors = {
            0: (255, 255, 255),  # White
            1: (255, 165, 0),    # Orange
            2: (0, 255, 0),      # Green
            3: (255, 0, 0),      # Red
            4: (0, 0, 255),      # Blue
            5: (255, 255, 0),    # Yellow
        }
    
    def create_window(self):
        if not VISUALIZATION_AVAILABLE:
            return

        self.window = pyglet.window.Window(900, 700, "AI Rubik's Cube - Best Genome Visualization")
        self.batch = pyglet.graphics.Batch()

        @self.window.event
        def on_draw():
            self.window.clear()
            self.draw()

        @self.window.event
        def on_close():
            global visualization_running
            visualization_running = False
            self.window.close()

        # Create initial squares for the cube net
        self.create_cube_net()
    
    def create_cube_net(self):
        """Create a 2D cube net layout for visualization"""
        self.squares = []
        square_size = 40
        gap = 2

        # Define the cube net layout positions
        # Format: (face_index, grid_x, grid_y)
        net_layout = [
            # Top face (0)
            (0, 1, 0),
            # Middle row: Left(1), Front(2), Right(3), Back(4)
            (1, 0, 1), (2, 1, 1), (3, 2, 1), (4, 3, 1),
            # Bottom face (5)
            (5, 1, 2)
        ]

        start_x = 50
        start_y = 50

        for face_idx, grid_x, grid_y in net_layout:
            face_squares = []
            for row in range(3):
                for col in range(3):
                    x = start_x + grid_x * (3 * square_size + gap * 3) + col * (square_size + gap)
                    y = start_y + grid_y * (3 * square_size + gap * 3) + row * (square_size + gap)

                    # Create a rectangle shape
                    square = shapes.Rectangle(x, y, square_size, square_size,
                                            color=self.colors[0], batch=self.batch)
                    face_squares.append(square)

            self.squares.append(face_squares)

    def draw(self):
        if not self.window:
            return

        # Update square colors based on current cube state
        self.update_square_colors()

        # Draw the batch
        self.batch.draw()

        # Draw text information
        self.draw_info()

    def update_square_colors(self):
        """Update the colors of squares based on current cube state"""
        for face_idx in range(6):
            if face_idx < len(self.squares):
                for square_idx in range(9):
                    if square_idx < len(self.squares[face_idx]):
                        state_idx = face_idx * 9 + square_idx
                        color_value = self.cube_state[state_idx]
                        self.squares[face_idx][square_idx].color = self.colors[color_value]

    def draw_info(self):
        """Draw text information about current state"""
        # Create labels for current move and fitness
        move_label = pyglet.text.Label(f'Current Move: {self.current_move}',
                                     font_name='Arial', font_size=16,
                                     x=50, y=self.window.height - 50,
                                     anchor_x='left', anchor_y='center')

        fitness_label = pyglet.text.Label(f'Fitness: {self.fitness_score}',
                                        font_name='Arial', font_size=16,
                                        x=50, y=self.window.height - 80,
                                        anchor_x='left', anchor_y='center')

        move_label.draw()
        fitness_label.draw()
    
    def update_cube_state(self, new_state, move="", fitness=0):
        self.cube_state = new_state.copy()
        self.current_move = move
        self.fitness_score = fitness
    
    def run_visualization(self):
        if not VISUALIZATION_AVAILABLE:
            print("Visualization not available - missing dependencies")
            return
            
        self.create_window()
        if self.window:
            global visualization_running
            visualization_running = True
            pyglet.app.run()

def fitness_scorer(cube):
    score = 0
    expectedValues = [0]*9 + [1]*9 + [2]*9 + [3]*9 + [4]*9 + [5]*9
    matching = sum(1 for x, y in zip(cube.state, expectedValues) if x == y)

    score += matching * 2

    for i in range(6):
        face = cube.state[i*9:(i+1)*9]
        if all(x == face[0] for x in face):
            score += 10

    top = cube.state[0*9:1*9]
    middle_faces = [1, 2, 3, 4]
    bottom = cube.state[5*9:6*9]

    if all(x == top[0] for x in top):
        score += 25

    if all(
        all(cube.state[f*9 + i] == cube.state[f*9] for i in [3, 4, 5])
        for f in middle_faces
    ):
        score += 40

    if all(x == bottom[0] for x in bottom):
        score += 25

    solved = matching == 54
    if solved:
        score += 1000

    return score, solved

def demonstrate_best_genome(visualizer=None):
    """Demonstrate the best genome with visualization"""
    global current_best_genome
    
    if current_best_genome is None:
        if os.path.exists("best_genome.pkl"):
            with open("best_genome.pkl", "rb") as f:
                current_best_genome = pickle.load(f)
        else:
            print("No best genome found!")
            return
    
    # Load config to create network
    local_dir = os.path.dirname(__file__)
    config_path = os.path.join(local_dir, 'config-feedforward')
    config = neat.Config(neat.DefaultGenome, neat.DefaultReproduction, 
                        neat.DefaultSpeciesSet, neat.DefaultStagnation, config_path)
    
    net = neat.nn.FeedForwardNetwork.create(current_best_genome, config)
    moves = ["U", "U'", "U2", "L", "L'", "L2", "F", "F'", "F2", "R", "R'", "R2", "B", "B'", "B2", "D", "D'", "D2"]
    
    # Create a scrambled cube
    cube = CUBE(random.choices(moves, k=random.randint(10, 15)))
    print("Starting demonstration with scrambled cube...")

    if visualizer:
        initial_fitness, _ = fitness_scorer(cube)
        visualizer.update_cube_state(cube.state, "Initial", initial_fitness)

    solved = False
    fitness = 0

    for step in range(100):
        output = net.activate(cube.state)
        move = moves[output.index(max(output))]
        cube.turn(move)

        fitness, solved = fitness_scorer(cube)

        print(f"Step {step + 1}: Move {move}, Fitness: {fitness}")

        if visualizer:
            visualizer.update_cube_state(cube.state, move, fitness)
            time.sleep(visualizer.move_delay)

        if solved:
            print(f"SOLVED in {step + 1} steps!")
            break

    if not solved:
        print(f"Did not solve in 100 steps. Final fitness: {fitness}")

def eval_genomes(genomes, config):
    global current_best_genome
    moves = ["U", "U'", "U2", "L", "L'", "L2", "F", "F'", "F2", "R", "R'", "R2", "B", "B'", "B2", "D", "D'", "D2"]

    for _, genome in genomes:
        cube = CUBE(random.choices(moves, k=random.randint(10, 15)))
        genome.fitness = fitness_scorer(cube)
        net = neat.nn.FeedForwardNetwork.create(genome, config)

        for x in range(100):
            output = net.activate(cube.state)
            cube.turn(moves[output.index(max(output))])
            fitness, solved = fitness_scorer(cube)
            genome.fitness = fitness - (x / 10)

            global best_so_far

            if genome.fitness > best_so_far:
                with open("best_genome.pkl", "wb") as f:
                    pickle.dump(genome, f)
                best_so_far = genome.fitness
                current_best_genome = genome
                print(f"New best genome found! Fitness: {genome.fitness}")

            if solved:
                break

def run_training(config_file):
    config = neat.Config(neat.DefaultGenome, neat.DefaultReproduction, neat.DefaultSpeciesSet, neat.DefaultStagnation, config_file)
    p = neat.Population(config)
    p.add_reporter(neat.StdOutReporter(True))
    stats = neat.StatisticsReporter()
    p.add_reporter(stats)

    if os.path.exists("best_genome.pkl"):
        with open("best_genome.pkl", "rb") as f:
            best = pickle.load(f)
        p.population[best.key] = best

    winner = p.run(eval_genomes, 100)
    print(f"\nBest genome:\n{winner}")

def main():
    print("AI Rubik's Cube with 3D Visualization")
    print("1. Train new genomes")
    print("2. Demonstrate best genome with visualization")
    print("3. Demonstrate best genome without visualization")
    
    choice = input("Enter your choice (1-3): ").strip()
    
    if choice == "1":
        local_dir = os.path.dirname(__file__)
        config_path = os.path.join(local_dir, 'config-feedforward')
        run_training(config_path)
    elif choice == "2":
        visualizer = CubeVisualizer()
        if VISUALIZATION_AVAILABLE:
            # Run visualization in a separate thread
            viz_thread = threading.Thread(target=visualizer.run_visualization)
            viz_thread.daemon = True
            viz_thread.start()
            
            # Wait a moment for window to initialize
            time.sleep(1)
            
            # Run demonstration
            demonstrate_best_genome(visualizer)
            
            # Keep visualization running
            while visualization_running:
                time.sleep(0.1)
        else:
            print("Visualization not available - running without 3D display")
            demonstrate_best_genome()
    elif choice == "3":
        demonstrate_best_genome()
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()
