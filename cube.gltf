{"asset": {"generator": "Khronos glTF Blender I/O v4.3.47", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]}], "nodes": [{"mesh": 0, "name": "<PERSON><PERSON><PERSON>", "translation": [-2, 2, -2]}, {"mesh": 1, "name": "Cubie.001", "translation": [0, 2, -2]}, {"mesh": 2, "name": "Cubie.002", "translation": [2, 2, -2]}, {"mesh": 3, "name": "Cubie.003", "translation": [-2, 2, 0]}, {"mesh": 4, "name": "Cubie.004", "translation": [0, 2, 0]}, {"mesh": 5, "name": "Cubie.005", "translation": [2, 2, 0]}, {"mesh": 6, "name": "Cubie.006", "translation": [-2, 2, 2]}, {"mesh": 7, "name": "Cubie.007", "translation": [0, 2, 2]}, {"mesh": 8, "name": "Cubie.008", "translation": [2, 2, 2]}, {"mesh": 9, "name": "Cubie.009", "translation": [-2, 0, -2]}, {"mesh": 10, "name": "Cubie.010", "translation": [0, 0, -2]}, {"mesh": 11, "name": "Cubie.011", "translation": [2, 0, -2]}, {"mesh": 12, "name": "Cubie.012", "translation": [-2, 0, 0]}, {"mesh": 13, "name": "Cubie.014", "translation": [2, 0, 0]}, {"mesh": 14, "name": "Cubie.015", "translation": [-2, 0, 2]}, {"mesh": 15, "name": "Cubie.016", "translation": [0, 0, 2]}, {"mesh": 16, "name": "Cubie.017", "translation": [2, 0, 2]}, {"mesh": 17, "name": "Cubie.018", "translation": [-2, -2, -2]}, {"mesh": 18, "name": "Cubie.019", "translation": [0, -2, -2]}, {"mesh": 19, "name": "Cubie.020", "translation": [2, -2, -2]}, {"mesh": 20, "name": "Cubie.021", "translation": [-2, -2, 0]}, {"mesh": 21, "name": "Cubie.022", "translation": [0, -2, 0]}, {"mesh": 22, "name": "Cubie.023", "translation": [2, -2, 0]}, {"mesh": 23, "name": "Cubie.024", "translation": [-2, -2, 2]}, {"mesh": 24, "name": "Cubie.025", "translation": [0, -2, 2]}, {"mesh": 25, "name": "Cubie.026", "translation": [2, -2, 2]}], "materials": [{"doubleSided": true, "name": "Top", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Back", "pbrMetallicRoughness": {"baseColorFactor": [0, 0.06124597042798996, 0.4178851544857025, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Left", "pbrMetallicRoughness": {"baseColorFactor": [1, 0.09758737683296204, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Border", "pbrMetallicRoughness": {"baseColorFactor": [0, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Right", "pbrMetallicRoughness": {"baseColorFactor": [0.4735291004180908, 0.006048769690096378, 0.03433983400464058, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Front", "pbrMetallicRoughness": {"baseColorFactor": [0, 0.32777851819992065, 0.0648033544421196, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Down", "pbrMetallicRoughness": {"baseColorFactor": [1, 0.6653882265090942, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"name": "Cube.001", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 4, "NORMAL": 5, "TEXCOORD_0": 6}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 8, "NORMAL": 9, "TEXCOORD_0": 10}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 11, "NORMAL": 12, "TEXCOORD_0": 13}, "indices": 14, "material": 3}]}, {"name": "Cube.002", "primitives": [{"attributes": {"POSITION": 15, "NORMAL": 16, "TEXCOORD_0": 17}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 18, "NORMAL": 19, "TEXCOORD_0": 20}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 21, "NORMAL": 22, "TEXCOORD_0": 23}, "indices": 24, "material": 3}]}, {"name": "Cube.003", "primitives": [{"attributes": {"POSITION": 25, "NORMAL": 26, "TEXCOORD_0": 27}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 28, "NORMAL": 29, "TEXCOORD_0": 30}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 32, "NORMAL": 33, "TEXCOORD_0": 34}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 35, "NORMAL": 36, "TEXCOORD_0": 37}, "indices": 38, "material": 3}]}, {"name": "Cube.004", "primitives": [{"attributes": {"POSITION": 39, "NORMAL": 40, "TEXCOORD_0": 41}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 42, "NORMAL": 43, "TEXCOORD_0": 44}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 45, "NORMAL": 46, "TEXCOORD_0": 47}, "indices": 48, "material": 3}]}, {"name": "Cube.005", "primitives": [{"attributes": {"POSITION": 49, "NORMAL": 50, "TEXCOORD_0": 51}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 52, "NORMAL": 53, "TEXCOORD_0": 54}, "indices": 55, "material": 3}]}, {"name": "Cube.006", "primitives": [{"attributes": {"POSITION": 56, "NORMAL": 57, "TEXCOORD_0": 58}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 59, "NORMAL": 60, "TEXCOORD_0": 61}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 62, "NORMAL": 63, "TEXCOORD_0": 64}, "indices": 65, "material": 3}]}, {"name": "Cube.007", "primitives": [{"attributes": {"POSITION": 66, "NORMAL": 67, "TEXCOORD_0": 68}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 69, "NORMAL": 70, "TEXCOORD_0": 71}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 72, "NORMAL": 73, "TEXCOORD_0": 74}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 75, "NORMAL": 76, "TEXCOORD_0": 77}, "indices": 78, "material": 3}]}, {"name": "Cube.008", "primitives": [{"attributes": {"POSITION": 79, "NORMAL": 80, "TEXCOORD_0": 81}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 82, "NORMAL": 83, "TEXCOORD_0": 84}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 85, "NORMAL": 86, "TEXCOORD_0": 87}, "indices": 88, "material": 3}]}, {"name": "Cube.009", "primitives": [{"attributes": {"POSITION": 89, "NORMAL": 90, "TEXCOORD_0": 91}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 92, "NORMAL": 93, "TEXCOORD_0": 94}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 95, "NORMAL": 96, "TEXCOORD_0": 97}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 98, "NORMAL": 99, "TEXCOORD_0": 100}, "indices": 101, "material": 3}]}, {"name": "Cube.010", "primitives": [{"attributes": {"POSITION": 102, "NORMAL": 103, "TEXCOORD_0": 104}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 105, "NORMAL": 106, "TEXCOORD_0": 107}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 108, "NORMAL": 109, "TEXCOORD_0": 110}, "indices": 111, "material": 3}]}, {"name": "Cube.011", "primitives": [{"attributes": {"POSITION": 112, "NORMAL": 113, "TEXCOORD_0": 114}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 115, "NORMAL": 116, "TEXCOORD_0": 117}, "indices": 118, "material": 3}]}, {"name": "Cube.012", "primitives": [{"attributes": {"POSITION": 119, "NORMAL": 120, "TEXCOORD_0": 121}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 122, "NORMAL": 123, "TEXCOORD_0": 124}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 125, "NORMAL": 126, "TEXCOORD_0": 127}, "indices": 128, "material": 3}]}, {"name": "Cube.013", "primitives": [{"attributes": {"POSITION": 129, "NORMAL": 130, "TEXCOORD_0": 131}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 132, "NORMAL": 133, "TEXCOORD_0": 134}, "indices": 135, "material": 3}]}, {"name": "Cube.015", "primitives": [{"attributes": {"POSITION": 136, "NORMAL": 137, "TEXCOORD_0": 138}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 139, "NORMAL": 140, "TEXCOORD_0": 141}, "indices": 142, "material": 3}]}, {"name": "Cube.016", "primitives": [{"attributes": {"POSITION": 143, "NORMAL": 144, "TEXCOORD_0": 145}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 146, "NORMAL": 147, "TEXCOORD_0": 148}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 149, "NORMAL": 150, "TEXCOORD_0": 151}, "indices": 152, "material": 3}]}, {"name": "Cube.017", "primitives": [{"attributes": {"POSITION": 153, "NORMAL": 154, "TEXCOORD_0": 155}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 156, "NORMAL": 157, "TEXCOORD_0": 158}, "indices": 159, "material": 3}]}, {"name": "Cube.018", "primitives": [{"attributes": {"POSITION": 160, "NORMAL": 161, "TEXCOORD_0": 162}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 163, "NORMAL": 164, "TEXCOORD_0": 165}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 166, "NORMAL": 167, "TEXCOORD_0": 168}, "indices": 169, "material": 3}]}, {"name": "Cube.019", "primitives": [{"attributes": {"POSITION": 170, "NORMAL": 171, "TEXCOORD_0": 172}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 173, "NORMAL": 174, "TEXCOORD_0": 175}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 176, "NORMAL": 177, "TEXCOORD_0": 178}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 179, "NORMAL": 180, "TEXCOORD_0": 181}, "indices": 182, "material": 3}]}, {"name": "Cube.020", "primitives": [{"attributes": {"POSITION": 183, "NORMAL": 184, "TEXCOORD_0": 185}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 186, "NORMAL": 187, "TEXCOORD_0": 188}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 189, "NORMAL": 190, "TEXCOORD_0": 191}, "indices": 192, "material": 3}]}, {"name": "Cube.021", "primitives": [{"attributes": {"POSITION": 193, "NORMAL": 194, "TEXCOORD_0": 195}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 196, "NORMAL": 197, "TEXCOORD_0": 198}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 199, "NORMAL": 200, "TEXCOORD_0": 201}, "indices": 7, "material": 1}, {"attributes": {"POSITION": 202, "NORMAL": 203, "TEXCOORD_0": 204}, "indices": 205, "material": 3}]}, {"name": "Cube.022", "primitives": [{"attributes": {"POSITION": 206, "NORMAL": 207, "TEXCOORD_0": 208}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 209, "NORMAL": 210, "TEXCOORD_0": 211}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 212, "NORMAL": 213, "TEXCOORD_0": 214}, "indices": 215, "material": 3}]}, {"name": "Cube.023", "primitives": [{"attributes": {"POSITION": 216, "NORMAL": 217, "TEXCOORD_0": 218}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 219, "NORMAL": 220, "TEXCOORD_0": 221}, "indices": 222, "material": 3}]}, {"name": "Cube.024", "primitives": [{"attributes": {"POSITION": 223, "NORMAL": 224, "TEXCOORD_0": 225}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 226, "NORMAL": 227, "TEXCOORD_0": 228}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 229, "NORMAL": 230, "TEXCOORD_0": 231}, "indices": 232, "material": 3}]}, {"name": "Cube.025", "primitives": [{"attributes": {"POSITION": 233, "NORMAL": 234, "TEXCOORD_0": 235}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 236, "NORMAL": 237, "TEXCOORD_0": 238}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 239, "NORMAL": 240, "TEXCOORD_0": 241}, "indices": 7, "material": 2}, {"attributes": {"POSITION": 242, "NORMAL": 243, "TEXCOORD_0": 244}, "indices": 245, "material": 3}]}, {"name": "Cube.026", "primitives": [{"attributes": {"POSITION": 246, "NORMAL": 247, "TEXCOORD_0": 248}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 249, "NORMAL": 250, "TEXCOORD_0": 251}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 252, "NORMAL": 253, "TEXCOORD_0": 254}, "indices": 255, "material": 3}]}, {"name": "Cube.027", "primitives": [{"attributes": {"POSITION": 256, "NORMAL": 257, "TEXCOORD_0": 258}, "indices": 31, "material": 5}, {"attributes": {"POSITION": 259, "NORMAL": 260, "TEXCOORD_0": 261}, "indices": 31, "material": 4}, {"attributes": {"POSITION": 262, "NORMAL": 263, "TEXCOORD_0": 264}, "indices": 31, "material": 6}, {"attributes": {"POSITION": 265, "NORMAL": 266, "TEXCOORD_0": 267}, "indices": 268, "material": 3}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 3, "componentType": 5123, "count": 6, "type": "SCALAR"}, {"bufferView": 4, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 5, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 6, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 7, "componentType": 5123, "count": 6, "type": "SCALAR"}, {"bufferView": 8, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 9, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 10, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 11, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 12, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 13, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 14, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 15, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 16, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 17, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 18, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 19, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 20, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 21, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 22, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 23, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 24, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 25, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 26, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 27, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 28, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 29, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 30, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 31, "componentType": 5123, "count": 6, "type": "SCALAR"}, {"bufferView": 32, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 33, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 34, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 35, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 36, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 37, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 38, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 39, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 40, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 41, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 42, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 43, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 44, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 45, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 46, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 47, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 48, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 49, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 50, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 51, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 52, "componentType": 5126, "count": 16, "max": [1, 1, 1], "min": [-1, 1, -1], "type": "VEC3"}, {"bufferView": 53, "componentType": 5126, "count": 16, "type": "VEC3"}, {"bufferView": 54, "componentType": 5126, "count": 16, "type": "VEC2"}, {"bufferView": 55, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 56, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 57, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 58, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 59, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 60, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 61, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 62, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 63, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 64, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 65, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 66, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 67, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 68, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 69, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 70, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 71, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 72, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 73, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 74, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 75, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 76, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 77, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 78, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 79, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 80, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 81, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 82, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 83, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 84, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 85, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 86, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 87, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 88, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 89, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 1, 0.9050000309944153], "min": [-0.9050000309944153, 1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 90, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 91, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 92, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 93, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 94, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 95, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 96, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 97, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 98, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 99, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 100, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 101, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 102, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 103, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 104, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 105, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 106, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 107, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 108, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 109, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 110, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 111, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 112, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 113, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 114, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 115, "componentType": 5126, "count": 16, "max": [1, 1, -1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 116, "componentType": 5126, "count": 16, "type": "VEC3"}, {"bufferView": 117, "componentType": 5126, "count": 16, "type": "VEC2"}, {"bufferView": 118, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 119, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 120, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 121, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 122, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 123, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 124, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 125, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 126, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 127, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 128, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 129, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 130, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 131, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 132, "componentType": 5126, "count": 16, "max": [-1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 133, "componentType": 5126, "count": 16, "type": "VEC3"}, {"bufferView": 134, "componentType": 5126, "count": 16, "type": "VEC2"}, {"bufferView": 135, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 136, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 137, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 138, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 139, "componentType": 5126, "count": 16, "max": [1, 1, 1], "min": [1, -1, -1], "type": "VEC3"}, {"bufferView": 140, "componentType": 5126, "count": 16, "type": "VEC3"}, {"bufferView": 141, "componentType": 5126, "count": 16, "type": "VEC2"}, {"bufferView": 142, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 143, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 144, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 145, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 146, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 147, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 148, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 149, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 150, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 151, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 152, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 153, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 154, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 155, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 156, "componentType": 5126, "count": 16, "max": [1, 1, 1], "min": [-1, -1, 1], "type": "VEC3"}, {"bufferView": 157, "componentType": 5126, "count": 16, "type": "VEC3"}, {"bufferView": 158, "componentType": 5126, "count": 16, "type": "VEC2"}, {"bufferView": 159, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 160, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 161, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 162, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 163, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 164, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 165, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 166, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 167, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 168, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 169, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 170, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 171, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 172, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 173, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 174, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 175, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 176, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 177, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 178, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 179, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 180, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 181, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 182, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 183, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 184, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 185, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 186, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 187, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 188, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 189, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 190, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 191, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 192, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 193, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 194, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 195, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 196, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 197, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 198, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 199, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, -1], "min": [-0.9050000309944153, -0.9050000309944153, -1], "type": "VEC3"}, {"bufferView": 200, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 201, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 202, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 203, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 204, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 205, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 206, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 207, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 208, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 209, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 210, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 211, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 212, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 213, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 214, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 215, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 216, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 217, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 218, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 219, "componentType": 5126, "count": 16, "max": [1, -1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 220, "componentType": 5126, "count": 16, "type": "VEC3"}, {"bufferView": 221, "componentType": 5126, "count": 16, "type": "VEC2"}, {"bufferView": 222, "componentType": 5123, "count": 48, "type": "SCALAR"}, {"bufferView": 223, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 224, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 225, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 226, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 227, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 228, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 229, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 230, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 231, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 232, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 233, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 234, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 235, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 236, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 237, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 238, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 239, "componentType": 5126, "count": 4, "max": [-1, 0.9000000357627869, 0.9050000309944153], "min": [-1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 240, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 241, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 242, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 243, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 244, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 245, "componentType": 5123, "count": 144, "type": "SCALAR"}, {"bufferView": 246, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 247, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 248, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 249, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 250, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 251, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 252, "componentType": 5126, "count": 32, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 253, "componentType": 5126, "count": 32, "type": "VEC3"}, {"bufferView": 254, "componentType": 5126, "count": 32, "type": "VEC2"}, {"bufferView": 255, "componentType": 5123, "count": 96, "type": "SCALAR"}, {"bufferView": 256, "componentType": 5126, "count": 4, "max": [0.9000000357627869, 0.9000000357627869, 1], "min": [-0.9050000309944153, -0.9050000309944153, 1], "type": "VEC3"}, {"bufferView": 257, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 258, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 259, "componentType": 5126, "count": 4, "max": [1, 0.9000000357627869, 0.9050000309944153], "min": [1, -0.9050000309944153, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 260, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 261, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 262, "componentType": 5126, "count": 4, "max": [0.9000000357627869, -1, 0.9050000309944153], "min": [-0.9050000309944153, -1, -0.9000000357627869], "type": "VEC3"}, {"bufferView": 263, "componentType": 5126, "count": 4, "type": "VEC3"}, {"bufferView": 264, "componentType": 5126, "count": 4, "type": "VEC2"}, {"bufferView": 265, "componentType": 5126, "count": 48, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 266, "componentType": 5126, "count": 48, "type": "VEC3"}, {"bufferView": 267, "componentType": 5126, "count": 48, "type": "VEC2"}, {"bufferView": 268, "componentType": 5123, "count": 144, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 48, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 48, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 96, "target": 34962}, {"buffer": 0, "byteLength": 12, "byteOffset": 128, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 140, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 188, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 236, "target": 34962}, {"buffer": 0, "byteLength": 12, "byteOffset": 268, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 280, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 328, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 376, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 408, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 984, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 1560, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 1944, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 2232, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 2280, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 2328, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 2360, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 2408, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 2456, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 2488, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 2872, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 3256, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 3512, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 3704, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 3752, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 3800, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 3832, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 3880, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 3928, "target": 34962}, {"buffer": 0, "byteLength": 12, "byteOffset": 3960, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 3972, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 4020, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 4068, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 4100, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 4676, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 5252, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 5636, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 5924, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 5972, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 6020, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 6052, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 6100, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 6148, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 6180, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 6564, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 6948, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 7204, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 7396, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 7444, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 7492, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 7524, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 7716, "target": 34962}, {"buffer": 0, "byteLength": 128, "byteOffset": 7908, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 8036, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 8132, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 8180, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 8228, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 8260, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 8308, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 8356, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 8388, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 8772, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 9156, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 9412, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 9604, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 9652, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 9700, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 9732, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 9780, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 9828, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 9860, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 9908, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 9956, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 9988, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 10564, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 11140, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 11524, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 11812, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 11860, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 11908, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 11940, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 11988, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 12036, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 12068, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 12452, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 12836, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 13092, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 13284, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 13332, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 13380, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 13412, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 13460, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 13508, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 13540, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 13588, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 13636, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 13668, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 14244, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 14820, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 15204, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 15492, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 15540, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 15588, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 15620, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 15668, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 15716, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 15748, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 16132, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 16516, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 16772, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 16964, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 17012, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 17060, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 17092, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 17284, "target": 34962}, {"buffer": 0, "byteLength": 128, "byteOffset": 17476, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 17604, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 17700, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 17748, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 17796, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 17828, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 17876, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 17924, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 17956, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 18340, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 18724, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 18980, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 19172, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 19220, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 19268, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 19300, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 19492, "target": 34962}, {"buffer": 0, "byteLength": 128, "byteOffset": 19684, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 19812, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 19908, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 19956, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 20004, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 20036, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 20228, "target": 34962}, {"buffer": 0, "byteLength": 128, "byteOffset": 20420, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 20548, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 20644, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 20692, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 20740, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 20772, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 20820, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 20868, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 20900, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 21284, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 21668, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 21924, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 22116, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 22164, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 22212, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 22244, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 22436, "target": 34962}, {"buffer": 0, "byteLength": 128, "byteOffset": 22628, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 22756, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 22852, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 22900, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 22948, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 22980, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 23028, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 23076, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 23108, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 23492, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 23876, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 24132, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 24324, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 24372, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 24420, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 24452, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 24500, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 24548, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 24580, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 24628, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 24676, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 24708, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 25284, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 25860, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 26244, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 26532, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 26580, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 26628, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 26660, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 26708, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 26756, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 26788, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 27172, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 27556, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 27812, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 28004, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 28052, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 28100, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 28132, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 28180, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 28228, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 28260, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 28308, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 28356, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 28388, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 28964, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 29540, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 29924, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 30212, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 30260, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 30308, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 30340, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 30388, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 30436, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 30468, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 30852, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 31236, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 31492, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 31684, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 31732, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 31780, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 31812, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 32004, "target": 34962}, {"buffer": 0, "byteLength": 128, "byteOffset": 32196, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 32324, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 32420, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 32468, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 32516, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 32548, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 32596, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 32644, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 32676, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 33060, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 33444, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 33700, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 33892, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 33940, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 33988, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 34020, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 34068, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 34116, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 34148, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 34196, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 34244, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 34276, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 34852, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 35428, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 35812, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 36100, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 36148, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 36196, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 36228, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 36276, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 36324, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 36356, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 36740, "target": 34962}, {"buffer": 0, "byteLength": 256, "byteOffset": 37124, "target": 34962}, {"buffer": 0, "byteLength": 192, "byteOffset": 37380, "target": 34963}, {"buffer": 0, "byteLength": 48, "byteOffset": 37572, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 37620, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 37668, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 37700, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 37748, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 37796, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 37828, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 37876, "target": 34962}, {"buffer": 0, "byteLength": 32, "byteOffset": 37924, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 37956, "target": 34962}, {"buffer": 0, "byteLength": 576, "byteOffset": 38532, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 39108, "target": 34962}, {"buffer": 0, "byteLength": 288, "byteOffset": 39492, "target": 34963}], "buffers": [{"uri": "cube_data.bin", "byteLength": 39780}]}