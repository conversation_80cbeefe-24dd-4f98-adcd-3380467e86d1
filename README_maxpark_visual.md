# AI Rubik's Cube with Visual Demonstration

This is an enhanced version of `maxpark.py` that includes 2D visual representation of the Rubik's cube solving process.

## Features

- **Visual Cube Net**: Shows a 2D unfolded cube layout with real-time color updates
- **Real-time Updates**: Watch the AI solve the cube step by step
- **Move Tracking**: See each move being made and the current fitness score
- **Best Genome Demonstration**: Load and demonstrate the best trained genome

## Installation

First, install the required visualization library:

```bash
pip install pyglet
```

## Usage

Run the program:

```bash
python maxpark_visual.py
```

You'll be presented with three options:

1. **Train new genomes** - Run the NEAT algorithm to evolve new cube-solving networks
2. **Demonstrate best genome with visualization** - Show the best genome solving a scrambled cube with 2D visualization
3. **Demonstrate best genome without visualization** - Show the best genome solving without graphics (text only)

## Visualization Layout

The 2D cube net shows all 6 faces of the cube laid out in a cross pattern:

```
    [Top]
[L] [F] [R] [B]
    [Bottom]
```

Where:
- **Top**: White face (when solved)
- **L**: Left face (Orange when solved)
- **F**: Front face (Green when solved) 
- **R**: Right face (Red when solved)
- **B**: Back face (Blue when solved)
- **Bottom**: Bottom face (Yellow when solved)

## Color Coding

- **White**: Face 0 (Top)
- **Orange**: Face 1 (Left)
- **Green**: Face 2 (Front)
- **Red**: Face 3 (Right)
- **Blue**: Face 4 (Back)
- **Yellow**: Face 5 (Bottom)

## Controls

- The visualization window will show the current move being made
- The fitness score is displayed in real-time
- The solving process runs at a comfortable pace (0.5 seconds between moves)
- Close the window to stop the demonstration

## Files

- `maxpark_visual.py` - Main program with visualization
- `maxpark.py` - Original training program (ignored as requested)
- `simulation.py` - Cube simulation logic
- `config-feedforward` - NEAT configuration file
- `best_genome.pkl` - Saved best genome (created after training)

## How It Works

1. The program uses the same NEAT (NeuroEvolution of Augmenting Topologies) algorithm as the original
2. The cube state is represented as a 54-element array (6 faces × 9 squares each)
3. The neural network takes the cube state as input and outputs move probabilities
4. The visualization shows this process in real-time with a 2D cube net representation

## Performance

The AI will attempt to solve scrambled cubes in up to 100 moves. The fitness function rewards:
- Correct square positions (2 points each)
- Complete faces (10 points each)
- Solved top face (25 points)
- Solved middle band (40 points)
- Solved bottom face (25 points)
- Complete solution (1000 points)

A penalty is applied for longer solution sequences to encourage efficiency.
